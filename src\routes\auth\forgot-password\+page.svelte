<script>
  import { onMount } from 'svelte'

  let email = ''
  let loading = false
  let message = ''
  let error = ''

  const handleSubmit = async (e) => {
    e.preventDefault()
    error = ''
    message = ''
    loading = true

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      })

      const data = await response.json()

      if (response.ok) {
        message = data.message
        email = '' // 清空邮箱输入
      } else {
        error = data.error || '请求失败'
      }
    } catch {
      error = '网络错误，请稍后重试'
    } finally {
      loading = false
    }
  }

  onMount(() => {
    window.scrollTo(0, 0)
  })
</script>

<svelte:head>
  <title>忘记密码 - Excel学习平台</title>
  <meta name="description" content="重置您的Excel学习平台密码" />
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-6 px-4 sm:px-6 lg:px-8 pt-20">
  <div class="max-w-md w-full">
    <!-- 忘记密码卡片 -->
    <div class="bg-white shadow-2xl rounded-3xl p-8 border border-gray-100">
      <!-- 头部 -->
      <div class="text-center mb-8">
        <!-- Logo -->
        <div class="mx-auto w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
          <span class="text-white text-2xl">🔑</span>
        </div>

        <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
          忘记密码
        </h2>
        <p class="text-gray-600">
          输入您的邮箱地址，我们将发送密码重置链接
        </p>
        <p class="mt-4 text-sm text-gray-500">
          记起密码了？{' '}
          <a href="/auth/signin" class="font-semibold text-blue-600 hover:text-blue-700 transition-colors">
            返回登录 →
          </a>
        </p>
      </div>

      <!-- 成功消息 -->
      {#if message}
        <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p class="text-green-700 text-sm">{message}</p>
          </div>
        </div>
      {/if}

      <!-- 错误消息 -->
      {#if error}
        <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p class="text-red-700 text-sm">{error}</p>
          </div>
        </div>
      {/if}

      <!-- 忘记密码表单 -->
      <form class="space-y-6" on:submit={handleSubmit}>
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
            邮箱地址
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
              </svg>
            </div>
            <input
              id="email"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              placeholder="请输入注册时使用的邮箱地址"
              bind:value={email}
            />
          </div>
        </div>

        <!-- 发送重置链接按钮 -->
        <button
          type="submit"
          disabled={loading}
          class="w-full flex justify-center items-center py-3 px-4 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
        >
          {#if loading}
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            发送中...
          {:else}
            发送重置链接
            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          {/if}
        </button>
      </form>
    </div>
  </div>
</div>
