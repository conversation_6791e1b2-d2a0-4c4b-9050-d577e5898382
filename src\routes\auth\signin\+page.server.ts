import { fail, redirect } from '@sveltejs/kit'
import type { Actions, PageServerLoad } from './$types'
import { signIn } from '$lib/auth'

export const load: PageServerLoad = async ({ locals }) => {
  // 暂时移除认证检查以避免重定向循环
  // const session = await locals.auth()

  // // 如果已经登录，重定向到仪表板
  // if (session?.user) {
  //   throw redirect(302, '/dashboard')
  // }

  return {}
}

export const actions: Actions = {
  default: async ({ request, cookies }) => {
    const data = await request.formData()
    const email = data.get('email') as string
    const password = data.get('password') as string

    if (!email || !password) {
      return fail(400, {
        error: '请填写邮箱和密码'
      })
    }

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false
      })

      if (result?.error) {
        return fail(400, {
          error: '邮箱或密码错误'
        })
      }

      // 登录成功
      throw redirect(302, '/dashboard')
    } catch (error) {
      if (error instanceof Error && error.message.includes('redirect')) {
        throw error
      }
      
      return fail(500, {
        error: '登录失败，请稍后重试'
      })
    }
  }
}
