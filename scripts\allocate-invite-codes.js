import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 生成6位随机邀请码
function generateInviteCode(length = 6) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 为用户分配好友邀请码
async function allocateFriendCodes(userId, count) {
  try {
    const codes = []
    
    for (let i = 0; i < count; i++) {
      let code
      let isUnique = false
      
      // 确保生成的邀请码是唯一的
      while (!isUnique) {
        code = generateInviteCode(6)
        
        const existing = await prisma.friendInviteCode.findUnique({
          where: { code }
        })
        
        if (!existing) {
          isUnique = true
          codes.push(code)
          
          await prisma.friendInviteCode.create({
            data: {
              code,
              ownerId: userId
            }
          })
        }
      }
    }
    
    return codes
  } catch (error) {
    console.error('分配好友邀请码失败:', error)
    return []
  }
}

async function main() {
  try {
    // 查找所有经验值大于等于50且没有邀请码的用户
    const users = await prisma.user.findMany({
      where: {
        score: {
          gte: 50
        },
        userType: {
          in: ['normal', 'friend']
        }
      },
      include: {
        _count: {
          select: {
            friendInviteCodes: true
          }
        }
      }
    })

    console.log(`找到 ${users.length} 个符合条件的用户`)

    for (const user of users) {
      // 检查用户是否已经有邀请码
      const existingCodes = await prisma.friendInviteCode.count({
        where: {
          ownerId: user.id
        }
      })

      if (existingCodes === 0) {
        console.log(`为用户 ${user.username} (${user.email}) 分配邀请码...`)
        const codes = await allocateFriendCodes(user.id, 3)
        console.log(`成功分配了 ${codes.length} 个邀请码: ${codes.join(', ')}`)
      } else {
        console.log(`用户 ${user.username} 已有 ${existingCodes} 个邀请码，跳过`)
      }
    }

    console.log('邀请码分配完成！')
  } catch (error) {
    console.error('执行错误:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
