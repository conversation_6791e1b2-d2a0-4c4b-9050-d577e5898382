<script lang="ts">
  export let data: any

  $: session = data?.session
</script>

<svelte:head>
  <title>Excel学习平台 - 游戏闯关模式</title>
  <meta name="description" content="通过有趣的游戏闯关方式学习Excel，从基础操作到高级技巧" />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100">
  <!-- Hero Section -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex flex-col justify-center min-h-screen py-6">
      <div class="text-center">
        <div class="mb-6">
          <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 mb-4">
            🎮 游戏化学习体验
          </span>
        </div>
        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">
          掌握 <span class="text-green-600">Excel</span> 和 <span class="text-red-500">WPS</span> 表格
          <br />
          <div class="text-3xl md:text-4xl text-gray-700">从零基础到办公高手</div>
        </h1>
        <p class="text-xl text-gray-600 mb-4 max-w-3xl mx-auto leading-relaxed">
          通过闯关模式学习，在真实的表格环境中练习
          <br />
          让枯燥的学习变成有趣的挑战，快速提升职场竞争力
        </p>

        <!-- 数据统计 -->
        <div class="flex justify-center items-center space-x-8 mb-8 text-sm text-gray-600">
          <div class="flex items-center">
            <span class="text-2xl font-bold text-green-600">6</span>
            <span class="ml-1">大模块</span>
          </div>
          <div class="flex items-center">
            <span class="text-2xl font-bold text-green-600">20+</span>
            <span class="ml-1">个关卡</span>
          </div>
          <div class="flex items-center">
            <span class="text-2xl font-bold text-green-600">5</span>
            <span class="ml-1">个难度等级</span>
          </div>
        </div>

        <div class="mt-8 max-w-md mx-auto sm:flex sm:justify-center md:mt-10">
          <div class="rounded-md shadow-lg">
            <a
              href={session ? "/dashboard" : "/auth/signin"}
              class="w-full flex items-center justify-center px-10 py-4 border border-transparent text-lg font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
            >
              {session ? "继续学习 →" : "免费开始学习 →"}
            </a>
          </div>
          {#if !session}
            <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-4">
              <a
                href="/auth/signup"
                class="w-full flex items-center justify-center px-10 py-4 border border-gray-300 text-lg font-medium rounded-md text-green-600 bg-white hover:bg-gray-50 transition-all duration-200"
              >
                注册账户
              </a>
            </div>
          {/if}
        </div>

        <p class="mt-4 text-sm text-gray-500">
          💯 完全免费 • 🚀 即学即用 • 📈 进度跟踪
        </p>
      </div>

      <!-- 核心优势 -->
      <div class="mt-10">
        <h2 class="text-3xl font-bold text-center text-gray-900 mb-12">
          为什么选择我们的Excel和WPS表格学习平台？
        </h2>
        <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          <div class="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-r from-green-400 to-green-600 text-white mx-auto mb-4">
              <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">🎮 游戏化闯关</h3>
            <p class="text-gray-600 leading-relaxed">
              告别枯燥的教程，通过有趣的闯关模式学习，每完成一个任务都有成就感，让学习变成一种享受。
            </p>
          </div>

          <div class="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-r from-blue-400 to-blue-600 text-white mx-auto mb-4">
              <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">⚡ 实时练习</h3>
            <p class="text-gray-600 leading-relaxed">
              在真实的表格环境中练习，即时验证操作结果，边学边练，确保每个知识点都能真正掌握。
            </p>
          </div>

          <div class="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-r from-purple-400 to-purple-600 text-white mx-auto mb-4">
              <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">📊 进度跟踪</h3>
            <p class="text-gray-600 leading-relaxed">
              详细的学习进度跟踪，获得经验值奖励，清晰看到自己的成长轨迹，保持学习动力。
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 学习路径展示 -->
  <div class="bg-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
          系统化的学习路径
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          从Excel和WPS表格新手到办公高手，我们为你规划了完整的学习路径<br />循序渐进，稳步提升
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {#each [
          { name: "初识表格", tasks: 7, points: "10-20", icon: "🌱", color: "from-green-400 to-green-500" },
          { name: "基本公式", tasks: 4, points: "20-30", icon: "🧮", color: "from-blue-400 to-blue-500" },
          { name: "常用操作", tasks: 7, points: "25-40", icon: "⚙️", color: "from-purple-400 to-purple-500" },
          { name: "进阶公式", tasks: 2, points: "50", icon: "🚀", color: "from-orange-400 to-orange-500" },
          { name: "进阶操作", tasks: 3, points: "50-60", icon: "💎", color: "from-red-400 to-red-500" }
        ] as level, index}
          <div class="relative">
            <div class="bg-white rounded-lg shadow-lg p-6 border-2 border-gray-100 hover:border-green-300 transition-all duration-300 hover:shadow-xl">
              <div class={`w-12 h-12 rounded-full bg-gradient-to-r ${level.color} flex items-center justify-center text-white text-xl mb-4 mx-auto`}>
                {level.icon}
              </div>
              <h3 class="text-lg font-semibold text-gray-900 text-center mb-2">{level.name}</h3>
              <div class="text-center text-sm text-gray-600">
                <p>{level.tasks} 个任务</p>
                <p class="font-medium text-green-600">{level.points} 经验值</p>
              </div>
            </div>
            {#if index < 4}
              <div class="hidden lg:block absolute top-1/2 -right-3 transform -translate-y-1/2">
                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            {/if}
          </div>
        {/each}
      </div>
    </div>
  </div>

  <!-- 学习收益 -->
  <div class="bg-gradient-to-r from-green-600 to-emerald-600 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h2 class="text-3xl font-bold text-white mb-4">
          学会Excel和WPS表格，你将获得什么？
        </h2>
        <p class="text-xl text-green-100 mb-12 max-w-3xl mx-auto items-center">
          掌握Excel和WPS表格不仅仅是学会一个软件<br />更是提升工作效率、增强职场竞争力的关键技能
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="bg-white bg-opacity-20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span class="text-3xl">💼</span>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">提升工作效率</h3>
            <p class="text-green-100 text-sm">数据处理速度提升10倍，告别加班</p>
          </div>

          <div class="text-center">
            <div class="bg-white bg-opacity-20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span class="text-3xl">📈</span>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">职场竞争力</h3>
            <p class="text-green-100 text-sm">成为数据分析专家，获得更多机会</p>
          </div>

          <div class="text-center">
            <div class="bg-white bg-opacity-20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span class="text-3xl">💰</span>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">薪资提升</h3>
            <p class="text-green-100 text-sm">数据分析技能可带来15-30%薪资增长</p>
          </div>

          <div class="text-center">
            <div class="bg-white bg-opacity-20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span class="text-3xl">🎯</span>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">决策支持</h3>
            <p class="text-green-100 text-sm">用数据说话，做出更明智的决策</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 学习内容预览 -->
  <div class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
          你将学到什么？
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          从基础操作到高级技巧，全面掌握Excel和WPS表格的各项功能
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
              <span class="text-blue-600 font-bold">📊</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">基础操作</h3>
          </div>
          <ul class="text-gray-600 space-y-2 text-sm">
            <li>• 工作表创建与管理</li>
            <li>• 数据输入与格式设置</li>
            <li>• 单元格操作技巧</li>
            <li>• 基本公式运用</li>
          </ul>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
              <span class="text-green-600 font-bold">🧮</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">公式函数</h3>
          </div>
          <ul class="text-gray-600 space-y-2 text-sm">
            <li>• SUM、AVERAGE等基础函数</li>
            <li>• VLOOKUP、INDEX/MATCH查找</li>
            <li>• IF条件判断函数</li>
            <li>• 日期时间函数应用</li>
          </ul>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
              <span class="text-purple-600 font-bold">📈</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">数据分析</h3>
          </div>
          <ul class="text-gray-600 space-y-2 text-sm">
            <li>• 数据透视表制作</li>
            <li>• 图表创建与美化</li>
            <li>• 数据筛选与排序</li>
            <li>• 条件格式应用</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- 常见问题 -->
  <div id="faq" class="bg-white py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-3">
          常见问题解答
        </h2>
        <p class="text-lg text-gray-600">
          解答你可能关心的问题
        </p>
      </div>

      <div class="space-y-4">
        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">
            Q: 我是Excel和WPS表格零基础，能学会吗？
          </h3>
          <p class="text-gray-600">
            A: 当然可以！我们的内容从最基础的操作开始，循序渐进，即使是完全没有接触过Excel和WPS表格的用户也能轻松上手。
          </p>
        </div>

        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">
            Q: 学习需要多长时间？
          </h3>
          <p class="text-gray-600">
            A: 自学或者通过看视频学习可能需要很长时间。通过我们的平台学习，一般1-2周可以掌握基础操作，3-4周可以达到中高级水平。每天15-30分钟，轻松学会Excel和WPS表格。
          </p>
        </div>

        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">
            Q: 平台是否收费？
          </h3>
          <p class="text-gray-600">
            A: 平台完全免费使用，所有学习内容和功能都可以免费体验，我们致力于让每个人都能学会Excel和WPS表格。
          </p>
        </div>

        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">
            Q: 学习过程中遇到问题怎么办？
          </h3>
          <p class="text-gray-600">
            A: 每个任务都有详细的操作指导，如果操作错误会有即时提示。同时我们也在不断优化学习体验。
          </p>
        </div>

        <div id="faq-invite-code" class="bg-gray-50 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">
            Q: 邀请码是什么，如何获得邀请码？
          </h3>
          <p class="text-gray-600">
            A: 有两中邀请码，一种是内测用户邀请码，由网站派发或者联系**********************申请；另一种是好友邀请码。内测用户自动获得5个好友邀请码；非内测用户在闯关积累到50经验值后，平台奖励3个好友邀请码，可以邀请好友一起学习共同进步；或转赠他人注册时使用，帮助他人解锁专属内容。
          </p>
        </div>

        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">
            Q: 没有邀请码可以学习吗？
          </h3>
          <p class="text-gray-600">
            A: 当然可以！没有邀请码的用户可以学习非专属区域的内容，还可以通过闯关积累经验值，达到一定的经验值之后可以学习专属内容。
          </p>
        </div>

        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">
            Q: 使用邀请码注册和普通注册有什么不同？
          </h3>
          <p class="text-gray-600">
            A: 使用好友邀请码注册，可以自动解锁部分专属区域。使用内测用户邀请码注册，可以优先体验全部新功能、自动解锁全部模块、自动获得5个好友邀请码，同时享有专属徽章，彰显不同身份。普通注册用户，需要达到一定的经验值才可以访问专属区域。
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- 行动号召 -->
  <div class="bg-gradient-to-r from-green-600 to-emerald-600 py-16">
    <div class="max-w-5xl mx-auto text-center px-6 sm:px-8 lg:px-12">
      <h2 class="text-3xl font-bold text-white mb-4">
        准备好开始你的Excel和WPS表格学习之旅了吗？
      </h2>
      <p class="text-xl text-green-100 mb-8">
        加入我们，让Excel和WPS表格成为你职场路上的得力助手
      </p>

      <div class="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <a
          href={session ? "/dashboard" : "/auth/signup"}
          class="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-green-600 bg-white hover:bg-gray-50 transform hover:scale-105 transition-all duration-200 shadow-lg"
        >
          {session ? "立即开始学习" : "免费注册开始"}
          <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
          </svg>
        </a>

        {#if !session}
          <a
            href="/auth/signin"
            class="inline-flex items-center px-8 py-4 border-2 border-white text-lg font-medium rounded-md text-white hover:bg-white hover:text-green-600 transition-all duration-200"
          >
            已有账户？立即登录
          </a>
        {/if}
      </div>

      <p class="mt-6 text-sm text-green-100">
        🎉 注册即可立即开始 • 💯 完全免费 • 🚀 随时随地学习
      </p>
    </div>
  </div>

  <!-- 备案信息 -->
  <div class="bg-gray-100 py-4">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <a
          href="https://beian.miit.gov.cn/"
          target="_blank"
          rel="noopener noreferrer"
          class="text-sm text-gray-600 hover:text-gray-800 transition-colors duration-200"
        >
          津ICP备2025036128号-1
        </a>
      </div>
    </div>
  </div>
</div>
